package life.w1.pingpang.fragment;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import java.util.ArrayList;
import java.util.List;

import life.w1.pingpang.R;
import life.w1.pingpang.activity.AgentActivity;
import life.w1.pingpang.activity.EmotionActivity;
import life.w1.pingpang.activity.ExpressionRealTimeActivity;
import life.w1.pingpang.activity.EyeActivity;
import life.w1.pingpang.activity.HrvActivity;
import life.w1.pingpang.activity.SpeechActivity;
import life.w1.pingpang.utils.ConfigUtils;

public class ScreeningFragment extends Fragment {

    public ScreeningFragment() {

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return createDynamicLayout(inflater, container);
    }

    private View createDynamicLayout(LayoutInflater inflater, ViewGroup container) {
        // 创建主容器
        LinearLayout mainLayout = new LinearLayout(getContext());
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        // 定义所有可能的按钮
        List<ButtonInfo> allButtons = createButtonInfoList();

        // 根据配置过滤按钮
        List<ButtonInfo> visibleButtons = filterVisibleButtons(allButtons);

        // 动态创建布局
        createButtonRows(mainLayout, visibleButtons);

        return mainLayout;
    }

    public void showEyeTracker(View v) {
        this.requireActivity().startActivity(new Intent(this.getActivity(), EyeActivity.class));
    }

    public void showEmotion(View v) {
        this.requireActivity().startActivity(new Intent(this.getActivity(), EmotionActivity.class));
    }

    public void showSpeech(View v) {
        this.requireActivity().startActivity(new Intent(this.getActivity(), SpeechActivity.class));
    }

    public void showHrvTracker(View v) {
        this.requireActivity().startActivity(new Intent(this.getActivity(), HrvActivity.class));
    }

    public void showAgent(View v) {
        this.requireActivity().startActivity(new Intent(this.getActivity(), AgentActivity.class));
    }

    public void showExpressionRealTime(View v) {
        this.requireActivity().startActivity(new Intent(this.getActivity(), ExpressionRealTimeActivity.class));
    }

    // 眼动校准功能已注释
    // public void showEyeChecker(View v) {
    //     Intent intent = new Intent("com.mitdd.gazetracker.START_CALIBRATION");
    //     intent.setPackage("com.mitdd.gazetracker");
    //     this.requireActivity().sendBroadcast(intent);
    // }

    private List<ButtonInfo> createButtonInfoList() {
        List<ButtonInfo> buttons = new ArrayList<>();

        buttons.add(new ButtonInfo("btn_screening_hrv", R.drawable.hrv,
                getString(R.string.btn_screening_hrv_text), this::showHrvTracker));
        buttons.add(new ButtonInfo("btn_screening_eye", R.drawable.eye,
                getString(R.string.btn_screening_eye_text), this::showEyeTracker));
        buttons.add(new ButtonInfo("btn_screening_speech", R.drawable.speech,
                getString(R.string.btn_screening_speech_text), this::showSpeech));
        buttons.add(new ButtonInfo("btn_screening_emotion", R.drawable.emotion,
                getString(R.string.btn_screening_emotion_text), this::showEmotion));
        buttons.add(new ButtonInfo("btn_screening_expression_real_time", R.drawable.emotion_real,
                getString(R.string.btn_screening_expression_real_time_text), this::showExpressionRealTime));
        // 眼动校准按钮已注释
        // buttons.add(new ButtonInfo("btn_screening_calibration", R.drawable.calibration,
        //         getString(R.string.btn_screening_calibration_text), this::showEyeChecker));

        return buttons;
    }

    private List<ButtonInfo> filterVisibleButtons(List<ButtonInfo> allButtons) {
        List<ButtonInfo> visibleButtons = new ArrayList<>();
        String is_baichuan = ConfigUtils.isBaiChuan(getContext());
        boolean hideEvaluation = ConfigUtils.isHideEvaluationModules(getContext());

        for (ButtonInfo button : allButtons) {
            boolean shouldShow = true;

            // 根据is_baichuan配置过滤
            if (is_baichuan.equals("1")) {
                if (button.id.equals("btn_screening_expression_real_time")) {
                    shouldShow = false;
                }
            }

            // 根据hideEvaluation配置过滤
            if (hideEvaluation) {
                if (button.id.equals("btn_screening_eye") ||
                    // button.id.equals("btn_screening_calibration") ||  // 眼动校准已注释
                    button.id.equals("btn_screening_expression_real_time")) {
                    shouldShow = false;
                }
            }

            if (shouldShow) {
                visibleButtons.add(button);
            }
        }

        return visibleButtons;
    }

    private void createButtonRows(LinearLayout mainLayout, List<ButtonInfo> visibleButtons) {
        int buttonsPerRow = 3;
        int currentRow = 0;
        LinearLayout currentRowLayout = null;

        for (int i = 0; i < visibleButtons.size(); i++) {
            if (i % buttonsPerRow == 0) {
                // 创建新行
                if (currentRowLayout != null && currentRow > 0) {
                    // 添加行间距
                    View space = new View(getContext());
                    LinearLayout.LayoutParams spaceParams = new LinearLayout.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT, dpToPx(32));
                    space.setLayoutParams(spaceParams);
                    mainLayout.addView(space);
                }

                currentRowLayout = new LinearLayout(getContext());
                currentRowLayout.setOrientation(LinearLayout.HORIZONTAL);
                currentRowLayout.setLayoutParams(new LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
                mainLayout.addView(currentRowLayout);
                currentRow++;
            }

            // 创建按钮
            LinearLayout buttonLayout = createButtonLayout(visibleButtons.get(i));
            currentRowLayout.addView(buttonLayout);
        }
    }

    private LinearLayout createButtonLayout(ButtonInfo buttonInfo) {
        LinearLayout buttonLayout = new LinearLayout(getContext());
        buttonLayout.setOrientation(LinearLayout.VERTICAL);
        buttonLayout.setGravity(android.view.Gravity.CENTER);

        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT, 1.0f);
        buttonLayout.setLayoutParams(layoutParams);
        buttonLayout.setOnClickListener(buttonInfo.clickListener);

        // 创建ImageView
        ImageView imageView = new ImageView(getContext());
        LinearLayout.LayoutParams imageParams = new LinearLayout.LayoutParams(
                dpToPx(108), dpToPx(108));
        imageView.setLayoutParams(imageParams);
        imageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
        imageView.setImageResource(buttonInfo.iconRes);
        imageView.setContentDescription(buttonInfo.text);
        buttonLayout.addView(imageView);

        // 创建TextView
        TextView textView = new TextView(getContext());
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        textView.setLayoutParams(textParams);
        textView.setGravity(android.view.Gravity.CENTER);
        textView.setText(buttonInfo.text);
        textView.setTextSize(20);
        buttonLayout.addView(textView);

        return buttonLayout;
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    // ButtonInfo内部类
    private static class ButtonInfo {
        String id;
        int iconRes;
        String text;
        View.OnClickListener clickListener;

        ButtonInfo(String id, int iconRes, String text, View.OnClickListener clickListener) {
            this.id = id;
            this.iconRes = iconRes;
            this.text = text;
            this.clickListener = clickListener;
        }
    }

}